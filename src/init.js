const {
  createAuthenticationMiddleware,
  createAuthorizationMiddleware,
  createServiceAuthClient,
} = require('pigeon-cerberus');
const fetch = require('node-fetch');
const { Agent } = require('https');
const knex = require('knex');
const { omit } = require('lodash');
const jwksClient = require('jwks-rsa');
const jwt = require('jsonwebtoken');
const { name: APP_NAME, version: APP_VERSION } = require('../package.json');
const { createServer } = require('./server');
const CampaignClient = require('./components/campaign-api-client');
const InvestmentClient = require('./components/investment-api-client');
const ContentClient = require('./components/content-api-client');
const CredentialsClient = require('./components/credentials-api-client');
const DispositionsClient = require('./components/dispositions-client');
const Fetch = require('./components/fetch');
const CacheClient = require('./components/rule-api-cache-client');
const MarvelService = require('./components/marvel-client');
const productBookData = require('../data/productbook.json');
const ProductBook = require('./components/productbook');
const rateLimitMiddleware = require('./components/rate-limit/rate-limit-middleware');
const PigeonProxyClient = require('./components/pigeon-proxy-client');
const ProgidyWealthClient = require('./components/progidy-wealth-client');

const TargetedCampaignServiceFactory = require('./data-source/campaign/campaign-service-factory');
const { createVariableReplacer } = require('./components/variables');

const init = async (config, logger, redisClient, launchDarklyService) => {
  // initialize authentication middleware
  const authenticationMiddleware = createAuthenticationMiddleware({
    passportPublicKey: config.serviceAuth.publicKeyJWKS,
    passportExchangeOpaqueTokenURI: config.serviceAuth.exchangeOpaqueTokenURI,
    allowCustomerToken: config.serviceAuth.allowCustomerToken,
    allowOpaqueToken: config.serviceAuth.allowOpaqueToken,
    tokenClaimsPath: config.serviceAuth.tokenClaimsPath,
  });
  // initialize authorization middleware
  const authorizationMiddleware = createAuthorizationMiddleware({
    tokenClaimsPath: config.serviceAuth.tokenClaimsPath,
  });
  // initialize service auth client
  const serviceAuthClient = createServiceAuthClient({
    passportAccessTokenURI: config.serviceAuth.accessTokenURI,
    privateKey: config.serviceAuth.privateKey,
    privateKeyAlgorithm: config.serviceAuth.privateKeyAlgorithm,
    clientId: config.serviceAuth.clientId,
    scope: config.serviceAuth.scope,
    expiresIn: config.serviceAuth.expiresIn,
    notBefore: config.serviceAuth.notBefore,
  });
  // create keep alive http agent
  const keepAliveAgent = config.features.keepAlive
    ? new Agent({ keepAlive: true, keepAliveMsecs: 1000, maxSockets: 200, maxFreeSockets: 20 })
    : null;
  // initialize content service
  const contentService = ContentClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.contentAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.contentAPI.uri);
  // initialize old campaign service
  const ruleAPIClient = CampaignClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.campaignAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.campaignAPI.uri);
  // alert-rules
  const alertService = await CacheClient({
    logger,
    contentService,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.alertAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.alertAPI.uri, 'alerts', true, config.alertAPI.supportedLocales, config.alertAPI.cacheTTL);
  // campaign-rules
  const campaignCacheService = await CacheClient({
    logger,
    contentService,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.alertAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.alertAPI.uri, 'campaigns', false, [], config.campaignAPI.cacheTTL);
  // variable-mapping rules
  const variableMappingsCacheService = await CacheClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.alertAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.alertAPI.uri, 'variable_mappings', false, [], config.campaignAPI.cacheTTL);
    // variable-mapping rules
  const containerCacheService = await CacheClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.alertAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.alertAPI.uri, 'containers', false, [], config.campaignAPI.cacheTTL);
  // initialize insight service
  const targetedCampaignService = new TargetedCampaignServiceFactory({ logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.targetedCampaignAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
    launchDarklyService,
    config: config.targetedCampaignAPI });

  // initialize marvel service
  const marvelService = MarvelService({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.marvelAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
    launchDarklyService,
    redisClient,
  }, config.marvelAPI);

  const productBookService = ProductBook({
    config: config.marvelAPI.productBook,
    logger,
    productBook: productBookData,
    marvelService,
  });

  // initialize variable replacement service
  const investmentService = InvestmentClient({
    logger,
    fetch: Fetch({ fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.investmentAPI.timeout,
      clsNamespace: config.clsNamespace }),
  }, config.investmentAPI.uri);
  const variablesService = createVariableReplacer({ logger, productBookService, investmentService, variableMappingsCacheService });
  // initialize credential service
  const credentialsService = CredentialsClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.credentialsAPI.timeout,
      clsNamespace: config.clsNamespace,
    }),
    launchDarklyService,
  }, config.credentialsAPI);

  // initialize dispositions service
  const dispositionsDb = knex({
    client: 'mssql',
    connection: {
      server: config.mssql.server,
      port: config.mssql.port,
      user: config.mssql.user,
      password: config.mssql.password,
      database: config.mssql.database,
      timezone: config.mssql.timezone,
      options: config.mssql.options,
      requestTimeout: config.mssql.timeout,
    },
    pool: {
      min: config.mssql.min,
      max: config.mssql.max,
    },
  });
  const dispositionsService = DispositionsClient({
    db: dispositionsDb,
    logger,
    timeout: config.mssql.timeout,
    schema: config.mssql.schema,
    config: config.dispositions,
    redisClient,
    launchDarklyService,
  });
  const rateLimitingMiddleware = rateLimitMiddleware(launchDarklyService, logger, omit(config.rateLimiting, [ 'window' ]));

  // initialize pigeonAPI atlas Client
  const pigeonBffAtlasClient = PigeonProxyClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.pigeonAPIAtlas.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.pigeonAPIAtlas.uri);
  const pigeonBffPCFClient = PigeonProxyClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.pigeonAPIAtlas.timeout,
      clsNamespace: config.clsNamespace,
    }),
  }, config.pigeonAPIPCF.uri);

  // initialize prodigy wealth service
  const progidyWealthService = ProgidyWealthClient({
    logger,
    fetch: Fetch({
      fetch,
      agent: keepAliveAgent,
      authorizer: serviceAuthClient,
      timeout: config.progidyWealthAPI.timeout || 5000,
      clsNamespace: config.clsNamespace,
    }),
    launchDarklyService,
    redisClient,
  }, config.progidyWealthAPI.uri);

  // create http server
  const server = createServer({
    logger,
    authenticationMiddleware,
    authorizationMiddleware,
    alertService,
    targetedCampaignService,
    variableMappingsCacheService,
    ruleAPIClient,
    contentService,
    variablesService,
    credentialsService,
    launchDarklyService,
    marvelService,
    campaignCacheService,
    containerCacheService,
    dispositionsService,
    redisClient,
    ignoredLogRoutes: config.logging.ignoredLogRoutes,
    rateLimitingMiddleware,
    fetch,
    jwksClient,
    jwt,
    pigeonBffAtlasClient,
    pigeonBffPCFClient,
    progidyWealthService,
  }, config, config.space);

  // start HTTP listener
  const http = server.listen(config.http.port, () => {
    logger.info({ message: `${APP_NAME} v${APP_VERSION} started at :${config.http.port}` });
  });

  /**
   * Closes all services on shutdown and returns a Promise
   * @returns {Promise} Status
   */
  const onShutdown = async () => {
    return Promise.all([
      launchDarklyService.close(),
      dispositionsDb.destroy(),
    ]);
  };

  // graceful shutdown
  [ 'SIGINT', 'SIGTERM' ].forEach((signal) => process.on(signal, () => {
    const now = Date.now();
    logger.info({ message: `shutdown signal ${signal} received...` });
    // force exit on timeout
    setTimeout(() => {
      logger.info({ message: `forcing exit after ${config.http.gracefulTimeout} ms` });
      process.exit(1);
    }, config.http.gracefulTimeout);
    // shutdown
    http.close(() => {
      logger.info({ message: `HTTP server is down, ${Date.now() - now} ms` });
      onShutdown()
        .then(() => {
          logger.info({ message: `services disconnected successfully, ${Date.now() - now} ms` });
          process.exit(0);
        })
        .catch((err) => {
          logger.error({ message: `unable to disconnect services, ${Date.now() - now} ms`, err });
          process.exit(1);
        });
    });
  }));
};

module.exports = init;
