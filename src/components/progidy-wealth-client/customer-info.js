const { getResponse } = require('../common');

const customerInfo = async (
  { fetch, logger },
  basePath,
  {
    cardNumber,
    channelId = 'Mobile',
    country = 'CA',
    xOriginatingApplCode = 'BFB6',
    traceId,
    spanId,
  } = {},
) => {
  const opts = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
      'x-country-code': country,
      'x-channel-id': channelId,
      'x-originating-appl-code': xOriginatingApplCode,
      'x-b3-traceid': traceId,
      'x-b3-spanid': spanId,
    },
    body: JSON.stringify({
      scotia_card_number: cardNumber,
    }),
  };

  const now = new Date();
  try {
    const url = `${basePath}/api/v3/customer`;
    const result = await fetch(url, opts);
    const loggableReq = {
      request: {
        url,
        method: opts.method,
        headers: {
          ...opts.headers,
          'x-customer-scotiacard': '***',
        },
      },
    };

    const response = await getResponse(result);
    logger.info({
      message: 'customer request: get customer',
      ...loggableReq,
      response_time: new Date() - now,
    });

    return response.data;
  } catch (err) {
    logger.error({
      message: 'Error: getting customer',
      cause: String(err),
    });
    return Promise.reject(err);
  }
};

module.exports = customerInfo;
