const customerInfo = require('./customer-info');
const { getResponse } = require('../common');

// Mock the common module
jest.mock('../common', () => ({
  getResponse: jest.fn(),
}));

describe('customerInfo', () => {
  let mockFetch;
  let mockLogger;
  let basePath;
  let defaultParams;

  beforeEach(() => {
    mockFetch = jest.fn();
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    basePath = 'https://api.example.com';
    defaultParams = {
      cardNumber: '1234567890123456',
      traceId: 'trace-123',
      spanId: 'span-456',
    };

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('successful requests', () => {
    it('should make a successful request with default parameters', async () => {
      const mockResponseData = { customerId: '12345', name: '<PERSON>' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      const result = await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        defaultParams,
      );

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/api/v3/customer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'CA',
            'x-channel-id': 'Mobile',
            'x-originating-appl-code': 'BFB6',
            'x-b3-traceid': 'trace-123',
            'x-b3-spanid': 'span-456',
          },
          body: JSON.stringify({
            scotia_card_number: '1234567890123456',
          }),
        },
      );

      expect(getResponse).toHaveBeenCalledWith({
        ok: true,
        status: 200,
      });

      expect(mockLogger.info).toHaveBeenCalledWith({
        message: 'customer request: get customer',
        request: {
          url: `${basePath}/api/v3/customer`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'CA',
            'x-channel-id': 'Mobile',
            'x-originating-appl-code': 'BFB6',
            'x-b3-traceid': 'trace-123',
            'x-b3-spanid': 'span-456',
            'x-customer-scotiacard': '***',
          },
        },
        response_time: expect.any(Number),
      });

      expect(result).toEqual(mockResponseData);
    });

    it('should use custom parameters when provided', async () => {
      const customParams = {
        cardNumber: '9876543210987654',
        channelId: 'Web',
        country: 'US',
        xOriginatingApplCode: 'TEST',
        traceId: 'custom-trace',
        spanId: 'custom-span',
      };

      const mockResponseData = { customerId: '67890', name: 'Jane Smith' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      const result = await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        customParams,
      );

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/api/v3/customer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'US',
            'x-channel-id': 'Web',
            'x-originating-appl-code': 'TEST',
            'x-b3-traceid': 'custom-trace',
            'x-b3-spanid': 'custom-span',
          },
          body: JSON.stringify({
            scotia_card_number: '9876543210987654',
          }),
        },
      );

      expect(result).toEqual(mockResponseData);
    });

    it('should handle partial parameters with defaults', async () => {
      const partialParams = {
        cardNumber: '1111222233334444',
        country: 'UK',
        // channelId, xOriginatingApplCode should use defaults
        // traceId, spanId are undefined
      };

      const mockResponseData = { customerId: '11111', name: 'Bob Wilson' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        partialParams,
      );

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/api/v3/customer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'UK',
            'x-channel-id': 'Mobile',
            'x-originating-appl-code': 'BFB6',
            'x-b3-traceid': undefined,
            'x-b3-spanid': undefined,
          },
          body: JSON.stringify({
            scotia_card_number: '1111222233334444',
          }),
        },
      );
    });
  });

  describe('error handling', () => {
    it('should handle fetch errors', async () => {
      const fetchError = new Error('Network error');
      mockFetch.mockRejectedValue(fetchError);

      await expect(
        customerInfo(
          { fetch: mockFetch, logger: mockLogger },
          basePath,
          defaultParams,
        ),
      ).rejects.toThrow('Network error');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error: getting customer',
        cause: 'Error: Network error',
      });
    });

    it('should handle getResponse errors', async () => {
      const responseError = new Error('Invalid response');
      responseError.response = { error: 'Bad request' };

      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
      });
      getResponse.mockRejectedValue(responseError);

      await expect(
        customerInfo(
          { fetch: mockFetch, logger: mockLogger },
          basePath,
          defaultParams,
        ),
      ).rejects.toThrow('Invalid response');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error: getting customer',
        cause: 'Error: Invalid response',
      });
    });

    it('should handle HTTP error responses', async () => {
      const httpError = new Error('Bad Request');
      httpError.response = { message: 'Invalid card number' };

      mockFetch.mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
      });
      getResponse.mockRejectedValue(httpError);

      await expect(
        customerInfo(
          { fetch: mockFetch, logger: mockLogger },
          basePath,
          defaultParams,
        ),
      ).rejects.toThrow('Bad Request');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error: getting customer',
        cause: 'Error: Bad Request',
      });
    });
  });

  describe('logging behavior', () => {
    it('should mask sensitive data in logs', async () => {
      const mockResponseData = { customerId: '12345' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        defaultParams,
      );

      const logCall = mockLogger.info.mock.calls[0][0];
      expect(logCall.request.headers['x-customer-scotiacard']).toBe('***');
      expect(logCall.request.headers['x-customer-scotiacard']).not.toBe(defaultParams.cardNumber);
    });

    it('should log response time', async () => {
      const mockResponseData = { customerId: '12345' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        defaultParams,
      );

      const logCall = mockLogger.info.mock.calls[0][0];
      expect(logCall.response_time).toEqual(expect.any(Number));
      expect(logCall.response_time).toBeGreaterThanOrEqual(0);
    });
  });

  describe('edge cases', () => {
    it('should handle empty parameters object', async () => {
      const mockResponseData = { customerId: '12345' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        {},
      );

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/api/v3/customer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'CA',
            'x-channel-id': 'Mobile',
            'x-originating-appl-code': 'BFB6',
            'x-b3-traceid': undefined,
            'x-b3-spanid': undefined,
          },
          body: JSON.stringify({
            scotia_card_number: undefined,
          }),
        },
      );
    });

    it('should handle undefined parameters', async () => {
      const mockResponseData = { customerId: '12345' };
      const mockResponse = { data: mockResponseData };

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      });
      getResponse.mockResolvedValue(mockResponse);

      await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        // No parameters passed
      );

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/api/v3/customer`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=utf-8',
            Accept: 'application/json',
            'x-country-code': 'CA',
            'x-channel-id': 'Mobile',
            'x-originating-appl-code': 'BFB6',
            'x-b3-traceid': undefined,
            'x-b3-spanid': undefined,
          },
          body: JSON.stringify({
            scotia_card_number: undefined,
          }),
        },
      );
    });
  });
});
