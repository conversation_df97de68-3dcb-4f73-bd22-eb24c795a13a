const {
  aInB,
  filterArrayByCriteria,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  filterRulesByLanguageTargetting,
} = require('./massTargeting');

const productArray = [
  { ownership: 'R', code: 'CSS', sub_code: 'AB' },
  { ownership: 'R', code: 'MOR', sub_code: 'UN' },
  { ownership: 'R', code: 'MOR', sub_code: 'DI' },
  { ownership: 'R', code: 'SAV', sub_code: 'AU' },
  { ownership: 'R', code: 'SAV', sub_code: 'BB' },
  { ownership: 'R', code: 'SAV', sub_code: 'BM' },
  { ownership: 'R', code: 'SPL', sub_code: 'DI' },
  { ownership: 'R', code: 'STD', sub_code: '  ' },
  { ownership: 'R', code: 'VCL', sub_code: 'ZZ' },
  { ownership: 'R', code: 'TEQ', sub_code: '  ' },
  { ownership: 'R', code: 'BSA', sub_code: 'MP' },
  { ownership: 'R', code: 'TFS', sub_code: 'SB' },
  { ownership: 'R', code: 'RRI', sub_code: 'SB' },
  { ownership: 'R', code: 'TFS', sub_code: 'SB' },
  { ownership: 'R', code: 'REF', sub_code: 'SB' },
  { ownership: 'R', code: 'BSA', sub_code: 'MP' },
  { ownership: 'R', code: 'NRS', sub_code: 'PR' },
  { ownership: 'R', code: 'BSA', sub_code: 'P1' },
  { ownership: 'R', code: 'BSA', sub_code: 'P2' },
  { ownership: 'R', code: 'BSA', sub_code: 'P3' },
  { ownership: 'R', code: 'BSA', sub_code: 'P5' },
  { ownership: 'B', code: 'HEYO', sub_code: 'HAI' },
  { ownership: 'B', code: 'HSA', sub_code: 30 },
  { ownership: 'B', code: 'MAH', sub_code: 90 },
];

describe('mass targeting helpers', () => {
  describe('aInB', () => {
    const vehicleFilter = {
      type: 'car',
      colour: 'blue',
    };

    const vehicle = {
      type: 'car',
      colour: 'blue',
      make: 'Volkswagen',
      model: 'Golf',
      tires: 4,
      turbo: true,
    };

    test('a and b are empty objects', () => {
      expect(aInB({}, {})).toBeTruthy();
    });

    test(`all of a's properties and values are present in b`, () => {
      expect(aInB(vehicleFilter, vehicle)).toBeTruthy();
    });

    test(`b in a doesn't work`, () => {
      expect(aInB(vehicle, vehicleFilter)).toBeFalsy();
    });

    test(`some of a's properties and values are present in b`, () => {
      expect(aInB({ ...vehicleFilter, sunroof: true }, vehicle)).toBeFalsy();
    });

    test(`none of a's properties and values are present in b`, () => {
      const bunnyFilter = {
        legs: 4,
        ears: 2,
        name: 'Bugs',
      };
      expect(aInB(bunnyFilter, vehicle)).toBeFalsy();
    });

    test(`all of a's properties are present in b but some values are different`, () => {
      const motorcyleFilter = {
        type: 'motorcyle',
        colour: 'red',
        make: 'BMW',
        model: '9T Scrambler',
        tires: 2,
        turbo: false,
      };
      expect(aInB(motorcyleFilter, vehicle)).toBeFalsy();
    });
  });

  describe('arrayPassesFilterCriteria', () => {
    test('when filterCriteria is an empty object, then the array should pass', () => {
      expect(filterArrayByCriteria(productArray, {})).toHaveLength(productArray.length);
    });

    test('all filter criteria have empty array', () => {
      const filterCriteria = {
        'any_of': [],
        'all_of': [],
        'none_of': [],
      };
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);
    });

    test('any_of', () => {
      const filterCriteria = {
        'any_of': [
          { ownership: 'R' },
          { ownership: 'B' },
        ],
      };
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);

      // the same filter criteria against a single item with `R` ownership
      expect(filterArrayByCriteria([
        { ownership: 'R', code: 'ABC', sub_code: 'DEF' },
      ], filterCriteria)).toHaveLength(1);

      // with `B` ownership this time
      expect(filterArrayByCriteria([
        { ownership: 'B', code: 'ABC', sub_code: 'DEF' },
      ], filterCriteria)).toHaveLength(1);

      // change all the ownership values in the productArray to 'Z', it shouldn't match anything now
      const productsMangledOwnership = [ ...productArray ].map(p => ({ ...p })); // deeper clone
      productsMangledOwnership.forEach(p => { p.ownership = 'Z'; });
      expect(filterArrayByCriteria(productsMangledOwnership, filterCriteria)).toHaveLength(0);
    });

    test('all_of', () => {
      const filterCriteria = {
        'all_of': [
          { ownership: 'R' },
          { ownership: 'B' },
        ],
      };
      // this list must contain products with ownership for R and B
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);

      filterCriteria['all_of'].push({ ownership: 'R', code: 'CSS' });
      // this list must contain products with ownership for R and B and a product with ownership R and code CSS
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);

      filterCriteria['all_of'].push({ ownership: 'B', code: 'MAH', sub_code: 90 });
      // all criteria from previous cases + the object above
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);

      // try with something that doesn't exist
      filterCriteria['all_of'].push({ code: 'L1V' });
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(0);
    });

    test('none_of', () => {
      const filterCriteria = {
        'none_of': [
          { sub_code: '  ' },
        ],
      };

      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(0);

      filterCriteria['none_of'] = [
        { code: 'ABQ' },
        { ownership: 'A' },
        { ownership: 'Z' },
        { sub_code: ' ' }, // will pass as the items in product list are two spaces
        { ownership: 'R', code: 'CSS', sub_code: 'AC' }, // the first result is AB
      ];
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);
    });

    test('mixing and matching', () => {
      // only retail products
      let filterCriteria = {
        'all_of': [ { ownership: 'R' } ],
        'none_of': [ { ownership: 'B' } ],
      };

      const retailProducts = productArray.slice(0, 3); // all R ownerships
      expect(filterArrayByCriteria(retailProducts, filterCriteria)).toHaveLength(retailProducts.length);
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(0); // there are 3 B ownerships

      // the reverse of the above filter, only B ownership
      filterCriteria = {
        'all_of': [ { ownership: 'B' } ],
        'none_of': [ { ownership: 'R' } ],
      };
      const businessProducts = productArray.slice(-3); // last 3 elements
      expect(filterArrayByCriteria(businessProducts, filterCriteria)).toHaveLength(businessProducts.length);
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(0);

      // remove the filter criteria, target some of the items redundantly
      delete filterCriteria['none_of'];
      filterCriteria['any_of'] = [
        { code: 'HEYO' },
        { sub_code: 'HAI' },
        { code: 'CSS' },
      ];
      filterCriteria['all_of'].push({ sub_code: 'AB' });
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(4);
      filterCriteria['any_of'].push({ ownership: 'R' });
      expect(filterArrayByCriteria(productArray, filterCriteria)).toHaveLength(productArray.length);
    });
  });

  describe('filter rules by customer products', () => {
    // take the product array and add some unique ids to it
    productArray.forEach((p, index) => { p['account_key'] = `accountUniqueId-${index}`; });

    const createFakeRule = ({ index, pages, targetingCriteria }) => ({
      id: `fake-rule-${index}`,
      name: `Mock rule #${index}`,
      pages,
      mass_targeting: {
        v: 1,
        by_product: targetingCriteria,
      },
    });

    const rules = [
      createFakeRule({
        index: 1,
        pages: [ 'accounts' ],
        targetingCriteria: {
          all_of: [ { ownership: 'R' } ],
          none_of: [ { ownership: 'B' } ],
        },
      }),
      createFakeRule({
        index: 2,
        pages: [
          'cc',
          'chq',
          'sav',
        ],
        targetingCriteria: {
          all_of: [ { ownership: 'R' }, { ownership: 'B' } ],
          any_of: [ { ownership: 'R', code: 'CSS' } ],
        },
      }),
      createFakeRule({
        index: 3,
        pages: [
          'cc',
          'chq',
          'sav',
          'account-key', // will be replaced by any matched campaigns
          'tml',
          'acc',
          'bro',
        ],
        targetingCriteria: {
          all_of: [ { ownership: 'B' }, { ownership: 'B', code: 'MAH' } ],
          any_of: [ { ownership: 'R', code: 'CSS' } ],
        },
      }),
    ];

    test('expected rules exist', () => {
      const filteredRules = filterRulesByCustomerProducts(rules, productArray);
      expect(filteredRules).toHaveLength(2);

      expect(filteredRules[0].id).toEqual('fake-rule-2');
      expect(filteredRules[0]).toEqual(rules[1]);

      expect(filteredRules[1].id).toEqual('fake-rule-3');
      expect(filteredRules[1].pages).toEqual([
        'cc',
        'chq',
        'sav',
        'accountUniqueId-0', // the first item
        'accountUniqueId-21', // the following 3 are the B items
        'accountUniqueId-22',
        'accountUniqueId-23',
        'tml',
        'acc',
        'bro',
      ]);
    });
  });

  describe('ffilterRulesByCustomerScenePoints', () => {
    const createFakeRule = ({ index, targetingCriteria }) => ({
      id: `test-rule-${index}`,
      mass_targeting: {
        by_scene_points: targetingCriteria,
      },
    });

    const rules = [
      createFakeRule({
        index: 0,
        targetingCriteria: {
          targeting_criteria: 'range',
          range_min: 5000,
          range_max: 10000,
        },
      }),
      createFakeRule({
        index: 1,
        targetingCriteria: {
          targeting_criteria: 'less',
          points: 4000,
        },
      }),
      createFakeRule({
        index: 2,
        targetingCriteria: {
          targeting_criteria: 'lessEqual',
          points: 4000,
        },
      }),
      createFakeRule({
        index: 3,
        targetingCriteria: {
          targeting_criteria: 'equal',
          points: 8000,
        },
      }),
      createFakeRule({
        index: 4,
        targetingCriteria: {
          targeting_criteria: 'greater',
          points: 11000,
        },
      }),
      createFakeRule({
        index: 5,
        targetingCriteria: {
          targeting_criteria: 'greaterEqual',
          points: 11000,
        },
      }),
      {
        id: `test-rule-6`, // return rule if not targeted by scene points
        mass_targeting: { },
      },
      createFakeRule({ // do not return rule if scene points targeting is invalid
        index: 7,
        targetingCriteria: {
          targeting_criteria: 'invalid-criteria',
          points: 11000,
        },
      }),
    ];
    const testRule0 = 'test-rule-0';
    const testRule1 = 'test-rule-1';
    const testRule2 = 'test-rule-2';
    const testRule3 = 'test-rule-3';
    const testRule6 = 'test-rule-6';

    test('expected rules exist - less & lessEqual', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 2000);

      expect(filteredRules).toHaveLength(3);
      expect(filteredRules[0].id).toEqual(testRule1);
      expect(filteredRules[1].id).toEqual(testRule2);
      expect(filteredRules[2].id).toEqual('test-rule-6');
    });

    test('expected rules exist - lessEqual', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 4000);
      expect(filteredRules).toHaveLength(2);
      expect(filteredRules[0].id).toEqual(testRule2);
      expect(filteredRules[1].id).toEqual(testRule6);
    });

    test('expected rules exist - range', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 6000);
      expect(filteredRules).toHaveLength(2);
      expect(filteredRules[0].id).toEqual(testRule0);
      expect(filteredRules[1].id).toEqual(testRule6);
    });

    test('expected rules exist - range & equal', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 8000);
      expect(filteredRules).toHaveLength(3);
      expect(filteredRules[0].id).toEqual(testRule0);
      expect(filteredRules[1].id).toEqual(testRule3);
      expect(filteredRules[2].id).toEqual(testRule6);
    });

    test('expected rules exist - greaterEqual', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 11000);
      expect(filteredRules).toHaveLength(2);
      expect(filteredRules[0].id).toEqual('test-rule-5');
      expect(filteredRules[1].id).toEqual(testRule6);
    });

    test('expected rules exist - greater & greaterEqual', () => {
      const filteredRules = filterRulesByCustomerScenePoints(rules, 13000);
      expect(filteredRules).toHaveLength(3);
      expect(filteredRules[0].id).toEqual('test-rule-4');
      expect(filteredRules[1].id).toEqual('test-rule-5');
      expect(filteredRules[2].id).toEqual(testRule6);
    });
  });

  describe('filterRulesByLanguageTargetting', () => {
    const createFakeRule = ({ index, targetedLanguages }) => ({
      id: `test-rule-${index}`,
      mass_targeting: {
        languages: targetedLanguages,
      },
    });

    const rules = [
      createFakeRule({
        index: 0,
        targetedLanguages: [ 'en', 'fr' ],
      }),
      createFakeRule({
        index: 1,
        targetedLanguages: [ 'fr' ],
      }),
      createFakeRule({
        index: 2,
        targetedLanguages: [ 'en' ],
      }),
      createFakeRule({
        index: 3,
      }),
    ];

    const testRule0 = 'test-rule-0';
    const testRule3 = 'test-rule-3';

    test('expected rules exist - en language', () => {
      const filteredRules = filterRulesByLanguageTargetting(rules, 'en');

      expect(filteredRules).toHaveLength(3);
      expect(filteredRules[0].id).toEqual(testRule0);
      expect(filteredRules[1].id).toEqual('test-rule-2');
      expect(filteredRules[2].id).toEqual(testRule3);
    });

    test('expected rules exist - fr language', () => {
      const filteredRules = filterRulesByLanguageTargetting(rules, 'fr');

      expect(filteredRules).toHaveLength(3);
      expect(filteredRules[0].id).toEqual(testRule0);
      expect(filteredRules[1].id).toEqual('test-rule-1');
      expect(filteredRules[2].id).toEqual(testRule3);
    });
  });
});
