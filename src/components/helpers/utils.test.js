const { paginateRules } = require('./utils');

describe('paginateRules', () => {
  const rules = [
    'rule1',
    'rule2',
    'rule3',
    'rule4',
    'rule5',
    'rule6',
    'rule7',
    'rule8',
    'rule9',
    'rule10',
    'rule11',
  ];

  test('returns correct slice with valid offset and limit', () => {
    const result = paginateRules({ query: { offset: '0', limit: '10' }, rules });
    expect(result).toEqual(rules.slice(0, 10));
  });

  test('handles missing offset and limit', () => {
    const result = paginateRules({ query: {}, rules });
    expect(result).toEqual(rules);
  });

  test('handles non-numeric offset and limit', () => {
    const result = paginateRules({ query: { offset: 'abc', limit: 'xyz' }, rules });
    expect(result).toEqual(rules);
  });

  test('offset beyond array length returns empty array', () => {
    const result = paginateRules({ query: { offset: '15', limit: '2' }, rules });
    expect(result).toEqual([]);
  });

  test('limit exceeds array length', () => {
    const result = paginateRules({ query: { offset: '5', limit: '10' }, rules });
    expect(result).toEqual(rules.slice(5, rules.length));
  });
});
