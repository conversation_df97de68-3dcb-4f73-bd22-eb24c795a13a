const R = require('ramda');

/**
 * Checks if the properties and values in an object exist and are the same in another object
 * e.g. a = {ownership: 'R'}, b = {ownership: 'R', code: 'd'}; this would return true
 * @param {object} a - the first object
 * @param {object} b - the second object
 * @returns {boolean} - the result of if a's properties and values were found and were equal in b
 */
const aInB = (a, b) => {
  for (const aProp in a) {
    const aVal = a[`${aProp}`];

    if (!(aProp in b) || b[`${aProp}`] !== aVal) {
      return false;
    }
  }
  return true;
};

/**
 * A function to filter an array based on filtering criteria.
 * @param {array} array - an array of objects
 * @param {object} filterCriteria
 * - an object of the form {
 *   'any_of': [filterArray],
 *   'all_of': [filterArray],
 *   'none_of': [filterArray]
 * }
 * (filterCriteria can also be an empty object {} to indicate no filtering criteria meaning the function would return true)
 * [filterArray] is an array of objects to be used to match against that is supplied to the filter directives
 * Directives:
 * `any_of` specifies that the array must have at least one item in its [filterArray]
 * `all_of` specifies that the array must have all of items in its [filterArray]
 * `none_of` specifies that array must not have any of the items in its [filterArray]
 * @returns {array} - the products that passed the filter criteria
 */
const filterArrayByCriteria = (array, filterCriteria) => {
  if (R.isEmpty(filterCriteria)) {
    return array;
  }

  let filteredArray = [];
  const hasCriteriaFor = criteriaKey => filterCriteria[`${criteriaKey}`] && filterCriteria[`${criteriaKey}`].length > 0;
  const itemMatchesCriteria = (item, criteriaKey) => filterCriteria[`${criteriaKey}`].some(criteria => aInB(criteria, item));

  for (const item of array) {
    // check if this item matches any none_of criteria, if so return []
    if (hasCriteriaFor('none_of') && itemMatchesCriteria(item, 'none_of')) {
      return [];
    }

    let passesFilter = true;

    // determine if an items passes 'any_of' or 'all_of' criteria
    if (hasCriteriaFor('all_of') || hasCriteriaFor('any_of')) {
      passesFilter = [ 'all_of', 'any_of' ].some(criteriaKey => (
        hasCriteriaFor(criteriaKey) && itemMatchesCriteria(item, criteriaKey)
      ));
    }

    if (passesFilter) {
      filteredArray.push(item);
    }
  }

  // do a last check on the filtered items if we have `all_of` defined to ensure that it matches those criteria
  if (
    hasCriteriaFor('all_of') &&
    !filterCriteria['all_of'].every(criteria => array.some(product => aInB(criteria, product)))
  ) {
    return [];
  }

  return filteredArray;
};

/**
 * A function that filters rules based on if their filtering criteria matches the supplied products.
 * If `account-key` is supplied as a page name it will be replaced with the unique account ids that match
 * this rule.
 * @param rules { array } - rule api rules
 * @param products { array } - customer's marvel products i.e. [{ ownership, code, sub_code, account_key }]
 * @returns {array} - the list of rules that apply to the user's products
 */
const filterRulesByCustomerProducts = (rules, products) => (
  rules.filter(rule => {
    // if mass targeting is undefined, criteria should select all rules via {}
    const productTargetingCriteria = R.pathOr({}, [ 'mass_targeting', 'by_product' ], rule);
    const productsThatPassRuleFilter = filterArrayByCriteria(products, productTargetingCriteria);

    // check pages for account-key and replace with an appropriate accountKey from customer's products
    const dynamicPagesIndicator = rule.pages.indexOf('account-key');
    if (dynamicPagesIndicator !== -1) {
      // map products to just their account ids
      const accountKeys = productsThatPassRuleFilter
        .filter(p => p['account_key'])
        .map(p => p['account_key']);
      rule.pages.splice(dynamicPagesIndicator, 1, ...accountKeys);
    }

    return productsThatPassRuleFilter.length > 0;
  })
);

const filterRulesByCustomerScenePoints = (rules, customerScenePoints) => (
  rules.filter(rule => {
    const scenePointsTargetingCriteria = R.pathOr(null, [ 'mass_targeting', 'by_scene_points' ], rule);
    // rule is not targeted by scene points - return rule
    if (!scenePointsTargetingCriteria) {
      return true;
    }
    // rule is targeted by scene points but customer's scene points are undefined (i.e. failed api call, ld flag off) - do not return rule
    if (scenePointsTargetingCriteria.targeting_criteria && customerScenePoints === undefined) {
      return false;
    }
    // rule is targeted by scene points - validate against how many scene points customer has
    switch (scenePointsTargetingCriteria.targeting_criteria) {
      case 'equal':
        return customerScenePoints === scenePointsTargetingCriteria.points;
      case 'greater':
        return customerScenePoints > scenePointsTargetingCriteria.points;
      case 'greaterEqual':
        return customerScenePoints >= scenePointsTargetingCriteria.points;
      case 'less':
        return customerScenePoints < scenePointsTargetingCriteria.points;
      case 'lessEqual':
        return customerScenePoints <= scenePointsTargetingCriteria.points;
      case 'range':
        return customerScenePoints >= scenePointsTargetingCriteria.range_min && customerScenePoints <= scenePointsTargetingCriteria.range_max;
      default:
        return false;
    }
  })
);

const filterRulesByLanguageTargetting = (rules, language) => (
  rules.filter(rule => {
    const targetedLanguages = R.pathOr(null, [ 'mass_targeting', 'languages' ], rule);
    if (!targetedLanguages) {
      return true;
    }
    if (targetedLanguages && targetedLanguages.length > 0) {
      return targetedLanguages.includes(language);
    }
  })
);

module.exports = {
  aInB,
  filterArrayByCriteria,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  filterRulesByLanguageTargetting,
};
