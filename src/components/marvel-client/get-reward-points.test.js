const getRewardPoints = require('./get-reward-points');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockUri = 'https://cdb-int-rewards-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/rewards';
const cardNumber = '****************';
const language = 'en';
const preferredEnv = 'istred';
const channelId = 'Mobile';
const traceId = 'bd7a977555f6b982';
const spanId = '71200de3d3f82a83';

describe('Marvel Rewards API', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('should throw an error on reponse status >= 400', async () => {
    const mockResponse = {
      status: 400,
      ok: false,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: '',
        notifications: [ {
          code: 'MRVL_RW_REJECTED_ERR',
          source: 'MRVL_RW',
          message: 'Error connecting to Account Cache service.',
          uuid: '1a3f2a5e-5f4a-4696-9485-5f0811f82a7a',
          timestamp: '2021-09-14T09:41:26.831-0400',
          metadata: {
            SOURCE_ERR_CODE_1: 'MRVL_RW_ACCTCH_REJECTED',
          },
        } ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getRewardPoints({ fetch: mockFetch, logger: mockLogger }, mockUri, cardNumber, language, preferredEnv, channelId, traceId, spanId);
      expect(res).toBeDefined();
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
      expect(err).toHaveProperty('response');
    }
  });

  test('should successfully call', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: {
          reward_type: 'ScenePlus',
          cid: '***************',
          program_name: 'SCENE',
          scene_number: '6046469690672647',
          portfolio_balance: 600,
        },
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getRewardPoints({ fetch: mockFetch, logger: mockLogger }, mockUri, cardNumber, language, preferredEnv, channelId, traceId, spanId);
    expect(mockResponse.text).toBeCalled();
    expect(res).toEqual(600);
  });
});
