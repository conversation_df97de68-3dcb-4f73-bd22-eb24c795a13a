const getV2 = require('./v2-get');

const mockFetch = jest.fn();
const mockText = jest.fn();
const mockUri = 'https://cloud.bns';

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockText.mockClear();
  });
  test('should get campaign', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockRuleId = 'abcdef1234';
    const mockQuery = {
      deleted: false,
      disabled: false,
      sort: 'rules_api_id',
    };
    const mockPayload = { id: mockRuleId, title: 'some title' };
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getV2({ fetch: mockFetch }, mockUri, mockRuleId, mockQuery);
    expect(mockFetch).toHaveBeenCalled();
    expect(res).toEqual(mockPayload);
  });
});
