const qs = require('querystring');
const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

// //  const defaultQuery = {
// //    deleted: false,
// //    disabled: false,
// //    sort: 'rules_api_id',
// //  };

const getV2 = async ({ fetch }, basePath, id, query = {}) => {
  const result = await fetch(`${basePath}/v2/rules/${id}?${qs.stringify(query)}`, opts);
  return getResponse(result);
};

module.exports = getV2;
