/* eslint-disable security/detect-object-injection */
/* eslint-disable sonarjs/cognitive-complexity */
const R = require('ramda');
const _ = require('lodash');
const { orderBy, sortBy, cloneDeep } = require('lodash');
const Joi = require('@hapi/joi');
const promiseAllSettled = require('promise.allsettled');
const crypto = require('crypto');
const escapeHtml = require('escape-html');
const { HttpError } = require('../../errors');
const { getCampaignsSchema } = require('./validation');
const {
  filterRulesByVersion,
  filterByContainerAndPage,
  filterByEnrollmentStatus,
  filterByExternalRef,
  trim,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  filterByApplication,
  filterRulesByLanguageTargetting,
  getApplicationPlatform,
  getClientInfo,
  pickTruthy,
  resolveDynamicPages,
  getChannelId,
  getAgentUserAndXApplication,
  isCCAU,
  generateMessageIdsPega,
  getEmbedded,
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
} = require('../../helpers');
const {
  dispositionValues,
  messageSourceArr,
  validateCampaignRuleType,
  ORION_INBOX_CONTAINER_ID,
} = require('../../common');
const {
  priorityMessageCampaignId,
  isCampaignViewed,
  isPriorityMessageCampaign,
  isTargetedCampaign,
  isMassCampaign,
  massCampaignId,
  handleLDError,
  applications,
} = require('./common');
const momentTz = require('moment-timezone');
const { Promise } = require('bluebird');
const reDash = /-/g;
const logFile = 'get-campaigns.js';
const splunkErrorCodes = require('./common').splunkErrorCodes;

const getUniqueCampaignIds = R.pipe(R.map(R.prop('campaign_id')), R.uniq);

const filterUrgentPriorityMessageRules = R.filter((c) => R.and(isPriorityMessageCampaign(c), R.propOr(false, 'urgent', c)));
const filterNonUrgentPriorityMessageRules = R.filter((c) => R.and(isPriorityMessageCampaign(c), R.not(R.propOr(false, 'urgent', c))));

const filterUrgentTargetedCampaignRules = R.filter((c) => R.and(isTargetedCampaign(c), R.propOr(false, 'urgent', c)));
const filterNonUrgentTargetedCampaignRules = R.filter((c) => R.and(isTargetedCampaign(c), R.not(R.propOr(false, 'urgent', c))));

const filterUrgentMassCampaignRules = R.filter((c) => R.and(isMassCampaign(c), R.propOr(false, 'urgent', c)));
const filterNonUrgentMassCampaignRules = R.filter((c) => R.and(isMassCampaign(c), R.not(R.propOr(false, 'urgent', c))));

const setCampaignDisposition = async (targetedCampaignService, messageId, headerData, logger) => {
  const { customerToken, userIdentifier, channelId, xApplication, language, preferredEnv, useMock, spanId, traceId, xOriginatingApplCode, type, country, xFeatureFlagUid } = headerData;
  try {
    await targetedCampaignService.getInstance({ ruleType: type }).setDisposition({
      id: messageId,
      disposition: dispositionValues.viewed,
      customerToken,
      userIdentifier,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock,
      spanId,
      traceId,
      xOriginatingApplCode,
      country,
      xFeatureFlagUid,
    });
    logger.info({ message: 'Successfully updated campaigns to db disposition value', data: { messageId } });
  } catch (err) {
    logger.error({ message: 'Error in updating campaigns to db disposition value', err, messageId });
  }
};

const sortAndMapDispositions = async (targetedCampaignService, campaigns, rules, dbDispositions, logger, isDispositionsDbEnabled, headerData) => {
  const result = await Promise.all(rules.map(async rule => {
    const campaign = campaigns.find((c) => c.message_id === R.path([ 'external_ref', 'message_id' ], rule));

    const messageIds = campaign ? [ campaign.message_id ] : [ null ];

    // Support old and new message ids
    let oldMessageId = campaign && messageSourceArr.includes(campaign.message_source) && generateMessageIdsPega(campaign.campaign_id, campaign.language, campaign.message_source);
    if (oldMessageId && campaign.message_id !== oldMessageId) {
      messageIds.push(oldMessageId);
    }

    // List of dispositions is for a given card number & application, can have multiple dispositions (i.e. viewed, dismissed) per rule/message
    const dispositions = dbDispositions.filter(disp => (disp.rule_id === rule.id && messageIds.includes(disp.message_id)));
    const dispositionViewed = dispositions.some(disp => (disp.disposition === dispositionValues.viewed) && (disp.container !== ORION_INBOX_CONTAINER_ID));
    if (campaign) {
      const campaignViewed = isCampaignViewed(campaign);
      if (dispositions.length) {
        // If disposition db entries exist, the rule is either viewed or dismissed
        if (!campaignViewed) {
          const messageId = messageIds[0];
          await setCampaignDisposition(targetedCampaignService, messageId, headerData, logger);
        }
        if (dispositions.some(disp => disp.disposition === dispositionValues.dismissed || disp.disposition === dispositionValues.deleted)) return;
        rule.viewed = dispositionViewed;
      } else {
        // If disposition db entries do not exist, the rule is new
        rule.viewed = campaignViewed;
      }
    } else {
      if (dispositions.some(disp => disp.disposition === dispositionValues.dismissed || disp.disposition === dispositionValues.deleted)) return;
      rule.viewed = dispositionViewed;
    }

    // If launch drakly flag is off, set viewed property of mass/message rules prior to sorting by viewed
    if (isDispositionsDbEnabled === false && (R.path([ 'external_ref', 'campaign_id' ], rule) === priorityMessageCampaignId ||
      R.path([ 'external_ref', 'campaign_id' ], rule) === massCampaignId)) {
      rule.viewed = true;
    }
    return rule;
  }));
  return sortBy(result.filter(rule => rule), [ (o) => o.viewed ]);
};

const mapTargetedCampaigns = (campaigns, rules, mapExtRefData, customerProducts) => {
  const mapped = [];
  for (let i = 0; i < campaigns.length; i += 1) {
    const campaignId = R.prop('campaign_id', campaigns[i]);
    for (let j = 0; j < rules.length; j += 1) {
      if (campaignId === R.prop([ 'external_ref' ], rules[j])) {
        let recentDate = campaigns[i].start_date.replace(reDash, '');
        if (new Date(rules[j].start_at) > new Date(campaigns[i].start_date)) {
          recentDate = rules[j].start_at.split('T')[0].replace(reDash, '');
        }
        mapped.push({
          ...resolveDynamicPages(rules[j], customerProducts),
          start_date: recentDate,
          external_ref: pickTruthy({
            campaign_id: campaignId,
            message_id: campaigns[i].message_id,
            message_source: campaigns[i].message_source,
            data: mapExtRefData ? campaigns[i] : null,
          }),
        });
      }
    }
  }
  return mapped;
};

const mapMassCampaigns = (rules) => rules.map((rule) => ({
  ...rule,
  external_ref: {
    campaign_id: rule.external_ref,
    message_id: rule.id,
    message_source: 'DMS',
  },
  start_date: rule.start_at,
}));

/**
 * map campaign and product data to rule, then sort them.
 *
 * @param {Array} campaigns
 * @param {Array} customerProducts
 * @param {Array} rules
 * @param {boolean} mapExtRefData - map campaign object to external_ref.data of rule response
 * @returns
 */
const sortAndMapCampaigns = (campaigns, customerProducts, rules, mapExtRefData) => [
  // urgent / priority message campaign rules
  ...orderBy(
    mapMassCampaigns(filterUrgentPriorityMessageRules(rules)), [ 'start_at' ], [ 'desc' ],
  ),
  // urgent / targeted campaign rules
  ...orderBy(
    mapTargetedCampaigns(
      campaigns,
      filterUrgentTargetedCampaignRules(rules),
      mapExtRefData,
      customerProducts,
    ), [ 'start_date' ], [ 'desc' ],
  ),
  // urgent / mass campaign rules
  ...orderBy(
    mapMassCampaigns(filterRulesByCustomerProducts(filterUrgentMassCampaignRules(rules), customerProducts)), [ 'start_at' ], [ 'desc' ],
  ),
  // non-urgent / targeted campaign rules
  ...orderBy(
    mapTargetedCampaigns(
      campaigns,
      filterNonUrgentTargetedCampaignRules(rules),
      mapExtRefData,
      customerProducts,
    ), [ 'start_date' ], [ 'desc' ],
  ),
  // non-urgent / priority message campaign rules
  ...orderBy(
    mapMassCampaigns(filterNonUrgentPriorityMessageRules(rules)), [ 'start_at' ], [ 'desc' ],
  ),
  // non-urgent / mass campaign rules
  ...orderBy(
    mapMassCampaigns(filterRulesByCustomerProducts(filterNonUrgentMassCampaignRules(rules), customerProducts)), [ 'start_at' ], [ 'desc' ],
  ),
];

const getCampaigns = ({
  logger,
  contentService,
  targetedCampaignService,
  variablesService,
  campaignCacheService,
  containerCacheService,
  dispositionsService,
  launchDarklyService,
  marvelService,
}, config) => async (req, res, next) => {
  // ========== Joi validation ========== //
  const { error, value: query } = Joi.validate(req.query, getCampaignsSchema, { stripUnknown: true });
  if (error) {
    const errorDetails = error.details.map((err) => ({ path: err.path.join('.'), message: escapeHtml(err.message) }));
    return next(HttpError.badRequest('Validation error', errorDetails));
  }

  // ========== Access params/headers step ========== //
  const notifications = [];
  const {
    country,
    customerToken,
    cardNumber,
    uid,
    isAnonymousClient,
    anonymousRequestFlag,
  } = getClientInfo({ req, res, config });
  const { application, platform } = getApplicationPlatform(query.platform, query.application);
  const channelId = getChannelId({ req, application: query.application });
  const { xApplication } = getAgentUserAndXApplication({ req, channelId, query });
  const { language, languageContentful } = res.locals;
  // TODO: Implement a header processing middleware to store headers and pass down to other requests
  // get preferred-environment header in IST and UAT environments
  const preferredEnv = req.get('Preferred-Environment');
  // get fid flag for Insights pilot testing - PIGEON-5412
  const xFeatureFlagUid = req.get('x-feature-flag-uid');
  // get pigeon mocked Insight service flag for testing
  const mockedCampaign = req.get('X-Mock-Insight') !== undefined;
  const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex');
  const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex');
  const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6';
  const embedded = getEmbedded({ req });
  logger.info({ // short term monitoring to evaluate removal of getApplicationPlatform and getChannelId
    message: 'GET v1/get-campaigns request details',
    request: {
      query: {
        platform: query.platform,
        application: query.application,
      },
      headers: {
        xChannelId: req.get('x-channel-id'),
        spanId,
        traceId,
      },
    },
  });

  // ========== Get all active campaign rules and filter them by available query params ========== //
  let rules = Object.values(cloneDeep(R.pathOr([], [ 'campaigns' ], campaignCacheService.get())));
  rules = filterByContainerAndPage(rules, query.page, query.container, query.page_ex);
  if (R.pathOr(false, [ 'features', 'application' ], config)) {
    rules = filterByApplication(rules, application);
  }
  rules = filterRulesByVersion(rules, {
    platform: trim(platform),
    appVersion: trim(query.app_version),
    osVersion: trim(query.os_version),
    deviceModel: trim(query.device_model),
  });

  rules = filterRulesByLanguageTargetting(rules, language);

  // ========== Validate rule types ========== //
  const uniqueRuleTypes = _.uniq(_.map(rules, 'type'));
  const { error: ruleTypeError, value: ruleType } = validateCampaignRuleType(country, uniqueRuleTypes[0]);

  if (ruleTypeError) {
    return next(HttpError.badRequest(ruleTypeError));
  }
  // fetching from more than one downstream data source is not supported
  if (uniqueRuleTypes.length > 1) {
    logger.error({
      message: 'Get campaigns handler failed to return rules due to misconfigured application with more than one rule type.',
      details: { application, uniqueRuleTypes },
    });
    notifications.push({
      message: 'Application (placement) is misconfigured. More than one campaign rule type is not supported.',
      metadata: [ { application } ],
    });
    return res.status(200).json({ data: { total: 0, limit: query.limit, offset: query.offset, items: [] }, notifications });
  }
  const isCCAURuleType = isCCAU({ campaignRuleType: ruleType });

  // ========== Access LD flags ========== //
  let isDispositionsDbEnabled = true;
  let isAccountsEnabled = true;
  let isRewardsEnabled = true;
  let isCardProfileEnabled = true;

  const ldCaller = 'routes/get-campaigns';
  const ldFlagDb = 'pigeon-api.features.dispositions.db';
  const ldFlagAcct = 'pigeon-api.downstreams.marvel-account-cache';
  const ldFlagRewards = 'pigeon-api.downstreams.rewards';

  try {
    isDispositionsDbEnabled = uid && !isAnonymousClient && await launchDarklyService.isFeatureEnabled(ldFlagDb, true);
  } catch (err) {
    handleLDError({ err, flag: ldFlagDb, caller: ldCaller, logger });
  }
  try {
    isAccountsEnabled = cardNumber && !isAnonymousClient && application !== applications.abm && !isCCAURuleType &&
      await launchDarklyService.isFeatureEnabled(ldFlagAcct, true);
  } catch (err) {
    handleLDError({ err, flag: ldFlagAcct, caller: ldCaller, logger });
  }
  try {
    isRewardsEnabled = cardNumber && !isAnonymousClient && !isCCAURuleType && await launchDarklyService.isFeatureEnabled('pigeon-api.downstreams.rewards', true);
  } catch (err) {
    handleLDError({ err, flag: ldFlagRewards, caller: ldCaller, logger });
  }
  try {
    isCardProfileEnabled = !anonymousRequestFlag && !isCCAURuleType && await launchDarklyService.isFeatureEnabled('pigeon-api.downstreams.marvel-card-profile', true);
  } catch (err) {
    logger.error({ message: 'unable to call Launch Darkly for Marvel Card Profile from get-campaigns', err });
    handleLDError({ err, flag: 'pigeon-api.downstreams.marvel-card-profile', caller: 'routes/get-campaigns', logger });
  }

  const downstreamCalls = new Map();
  if (rules.some(rule => isTargetedCampaign(rule))) {
    downstreamCalls.set('campaigns', targetedCampaignService.getInstance({ ruleType }).getCampaigns({
      customerToken,
      userIdentifier: uid,
      channelId,
      xApplication,
      language,
      country,
      preferredEnv,
      spanId,
      traceId,
      useMock: mockedCampaign,
      xOriginatingApplCode,
      isAnonymousClient,
      xFeatureFlagUid,
    }));
  }
  if (isAccountsEnabled) {
    downstreamCalls.set('accounts', marvelService.getAccounts({ cardNumber, language, preferredEnv, xOriginatingApplCode }));
  }
  if (isCardProfileEnabled) {
    downstreamCalls.set('twoStepVerificationStatus', marvelService.getCardProfile({ cardNumber, preferredEnv, xOriginatingApplCode }));
  }
  if (isDispositionsDbEnabled) {
    downstreamCalls.set('dispositions', dispositionsService.getDispositions(uid, country, application));
  }

  const pendingPromises = [];
  const servicesCalled = [];
  downstreamCalls.forEach((value, key) => {
    pendingPromises.push(value);
    servicesCalled.push(key);
  });

  // issue 4 requests in parallel (targeted campaigns, accounts/products, dispositions, & enrollment status)
  const downstreamCallsResults = await promiseAllSettled(pendingPromises);
  const campaignsIndex = servicesCalled.indexOf('campaigns');
  const accountIndex = servicesCalled.indexOf('accounts');
  const dispositionsIndex = servicesCalled.indexOf('dispositions');
  const twoStepVerificationStatusIndex = servicesCalled.indexOf('twoStepVerificationStatus');
  const accountResponse = downstreamCallsResults[accountIndex];
  const campaignsResponse = downstreamCallsResults[campaignsIndex];
  const dispositionsResponse = downstreamCallsResults[dispositionsIndex];
  const twoStepVerificationResponse = downstreamCallsResults[twoStepVerificationStatusIndex];

  let campaigns = [];
  if (campaignsResponse && campaignsResponse.status === 'fulfilled') {
    campaigns = Array.isArray(campaignsResponse.value.data) ? campaignsResponse.value.data : [];
  } else {
    if (campaignsResponse && campaignsResponse.reason.message.indexOf('KTMGD-ACCT-NOT-FOUND') === -1) {
      logger.error({ message: 'unable to get insights campaigns', err: campaignsResponse.reason, code: 1009, description: splunkErrorCodes[1009] });
    }
  }

  let massCampaignRefs = [];
  let customerProducts = [];
  if (accountResponse && accountResponse.status === 'fulfilled') {
    massCampaignRefs = [ massCampaignId ];
    customerProducts = (accountResponse.value.accountList || [])
      .reduce((products, accountProduct) => {
        const {
          ownership,
          ciProductCode,
          ciProductSubCode,
          accountUniqueId,
        } = accountProduct;

        const product = {
          ownership,
          code: ciProductCode,
          sub_code: ciProductSubCode,
          account_key: accountUniqueId,
        };

        return [ ...products, product ];
      }, []);
  }

  let twoStepVerificationStatus;
  if (twoStepVerificationResponse && twoStepVerificationResponse.status === 'fulfilled') {
    twoStepVerificationStatus = twoStepVerificationResponse.value;
  }

  let dispositions = [];
  if (dispositionsResponse && dispositionsResponse.status === 'fulfilled') {
    dispositions = dispositionsResponse.value;
  }

  // build external refs filter to the cache
  const campaignsIds = getUniqueCampaignIds(campaigns);
  let externalRefs = [ ...campaignsIds, priorityMessageCampaignId, ...massCampaignRefs ];
  rules = filterByExternalRef(rules, externalRefs);

  rules = filterByEnrollmentStatus({ rules, twoStepVerificationStatus, deviceLock: query.secure_device });

  // filter mass campaigns by account response here
  // sort campaigns and map campaigns data to targeted campaign rules
  rules = sortAndMapCampaigns(campaigns, customerProducts, rules, query.insight);

  // if any rules target scene points, fetch cutomer's portfolio balance & filter by scene points
  if (rules.some(rule => rule.mass_targeting && rule.mass_targeting.by_scene_points)) {
    let customerScenePoints;
    try {
      if (isRewardsEnabled) {
        customerScenePoints = await marvelService.getRewardPoints(cardNumber, language, preferredEnv, channelId, traceId, spanId, xOriginatingApplCode);
      }
    } catch (err) { }
    rules = filterRulesByCustomerScenePoints(rules, customerScenePoints);
  }

  // remove dismissed campaigns, add viewed property to the remainders & sort by viewed
  if (uid && !isAnonymousClient) {
    rules = await sortAndMapDispositions(targetedCampaignService, campaigns, rules, dispositions, logger, isDispositionsDbEnabled, {
      customerToken,
      userIdentifier: uid,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock: mockedCampaign,
      spanId,
      traceId,
      xOriginatingApplCode,
      type: ruleType,
      country,
      xFeatureFlagUid,
    });
  }

  // ========== Apply container max limits ========== //
  // Build container name list from rules for limit lookup
  const containerNameList = Array.from(new Set(rules.map(rule => rule.container).filter(Boolean)));

  // Build container limit map
  const containers = Object.values(cloneDeep(R.pathOr([], [ 'containers' ], containerCacheService.get())));
  const maxRulePerContainerMap = buildRuleLimitPerContainerMap(containerNameList, containers);

  rules = applyContainerLimitToRuleList(rules, maxRulePerContainerMap);

  // apply the limit
  const contentOpts = { select: query.select_contents, language: languageContentful };
  let contents = [];
  try {
    // As long as the feature 'FEATURES_APPLICATION' have been enabled,
    // all the rules remaining have the same content space.
    const ruleWithSpaceId = rules.find((c) => !!c.content_space);
    if (ruleWithSpaceId) {
      const contentIds = R.pipe(R.map((r) => r.content_id), R.filter((id) => !!id), R.uniq)(rules);
      const contentItems = await contentService.getContentsByIds(ruleWithSpaceId.content_space, contentIds, contentOpts);
      if (contentItems && contentItems.data && Array.isArray(contentItems.data.items)) {
        contents = contentItems.data.items;
      }
    }
  } catch (err) {
    logger.error({ message: 'failed to get content', err, code: 1011, description: splunkErrorCodes[1011] });
  }

  // map contents to rules
  const result = [];
  const catchVarSvcErr = (rule, content) => err => {
    notifications.push({
      message: `failed to replace variables`,
      metadata: [ { rule_id: rule.id, message_id: R.path([ 'external_ref', 'message_id' ], rule) } ],
    });
    logger.warn({ message: 'failed to replace variables', err, rule, code: 1013, description: splunkErrorCodes[1013] });
    return { dataContext: 'unknown', transformed: {}, content: content.content };
  };
  const { offset, limit } = query;
  const startInd = Number(offset) * Number(limit);
  let total = rules.length;
  for (let i = startInd; i < rules.length && result.length < query.limit; i += 1) {
    const rule = rules[i];
    const content = contents.find((c) => c && c.id === rule.content_id);
    if (!content || !content.content) {
      total = total - 1;
      notifications.push({
        message: 'failed to get content',
        metadata: [ {
          rule_id: rule.id,
          content_space: rule.content_space,
          content_type: rule.content_type,
          content_id: rule.content_id,
        } ],
      });
      continue;
    }
    if (isTargetedCampaign(rule)) {
      rule.start_date = momentTz(rule.start_date, 'YYYY-MM-DD hh:mm:ss').tz('America/Toronto').utc().format();
    }

    const campaign = campaigns.find((c) => c.message_id === R.path([ 'external_ref', 'message_id' ], rule));
    const sessionId = req.get('x-session-id');
    const reqHeaders = {
      channelId,
      xApplication,
      sessionId,
      preferredEnv,
      xCustomerScotiacard: uid,
      spanId,
      traceId,
      xOriginatingApplCode,
      embedded,
    };

    const varsReplaced = await variablesService(
      preferredEnv,
      customerToken,
      content.content,
      campaign,
      rule,
      language,
      reqHeaders,
    ).catch(catchVarSvcErr(rule, content));

    const externalRef = { ...rule.external_ref };
    if (query.insight) {
      externalRef.data_context = R.pathOr(null, [ 'dataContext' ], varsReplaced);
      externalRef.data_transformed = R.pathOr({}, [ 'transformed' ], varsReplaced);
    }

    const dismissable = isDispositionsDbEnabled ? rule.dismissable_flag : false;

    const varsMapped = {
      id: rule.id,
      name: rule.name,
      type: content.type,
      container: rule.container,
      pages: rule.pages,
      urgent: rule.urgent,
      viewed: rule.viewed,
      dismissable,
      external_ref: externalRef,
      start_date: rule.start_date,
      content: varsReplaced.content,
      application: rule.application,
    };
    result.push(varsMapped);
  }
  const response = { data: { total: total, limit: query.limit, offset: query.offset, items: result }, notifications };
  logger.info({
    message: 'response to client',
    logFile,
    logType: 'response to client',
    response: {
      ...response,
      data: {
        ...response.data,
        items: response.data.items.map(i => ({
          ...i,
          external_ref: {
            ...i.external_ref,
            ...(R.path([ 'external_ref', 'data', 'additional_data' ], i) && {
              data: {
                ...i.external_ref.data,
                additional_data: null,
              },
            }),
            ...(R.path([ 'external_ref', 'data_transformed' ], i) && {
              data_transformed: null,
            }),
          },
          content: null,
        })),
      },
      request: {
        headers: {
          'x-b3-traceid': traceId,
        },
      },
    },
  });

  res.status(200).json(response);
};

module.exports = getCampaigns;
