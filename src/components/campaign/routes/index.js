const { Router } = require('express');
const R = require('ramda');
const wrapAsync = require('../../server/middleware/wrap-async');
const getCampaigns = require('./get-campaigns');
const getCampaign = require('./get-campaign');
const getSamlToken = require('./get-saml-token');
const flushCache = require('./flush-cache');
const setCampaignDisposition = require('./set-campaign-disposition');
const { scopes } = require('./common');
const maintenanceMode = require('../../server/middleware/maintenance-mode');

const tempScope = 'ca:baas:campaign-rules:read';

const init = ({
  logger,
  targetedCampaignService,
  contentService,
  campaignCacheService,
  containerCacheService,
  dispositionsService,
  variablesService,
  credentialsService,
  launchDarklyService,
  authorize,
  marvelService,
  redisClient,
  rateLimitingMiddleware,
  campaignsTotalRateLimiter,
  campaignsClientRateLimiter,
  fetch,
  jwksClient,
  jwt,
}, config) => {
  const router = Router();
  router.use(rateLimitingMiddleware, campaignsTotalRateLimiter, campaignsClientRateLimiter);
  // get a list of campaigns
  router.get(
    '/',
    [
      maintenanceMode({ launchDarklyService, hostingEnv: config.hostingEnv }, { code: 200, type: 'getCampaigns' }),
      authorize([ tempScope, scopes.campaignsRead, scopes.standard, scopes.legacy.campaignsRead ]),
    ],
    wrapAsync(getCampaigns({ logger, targetedCampaignService, contentService, variablesService, campaignCacheService, containerCacheService, dispositionsService, launchDarklyService, marvelService }, config)));
  router.get(
    '/:id',
    authorize([ tempScope, scopes.campaignsRead, scopes.standard, scopes.legacy.campaignsRead ]),
    wrapAsync(getCampaign({ logger, targetedCampaignService, contentService, variablesService, campaignCacheService, dispositionsService, launchDarklyService }, config)));
  // get SAML token for ScotiaHome flow
  router.get(
    '/:id/token',
    authorize([ tempScope, scopes.campaignsRead, scopes.standard, scopes.legacy.campaignsRead ]),
    wrapAsync(getSamlToken({ logger, targetedCampaignService, campaignCacheService, credentialsService, fetch, jwksClient, jwt }, config)));
  // set disposition for a campaign
  if (R.pathOr(false, [ 'features', 'disposition' ], config)) {
    router.post(
      '/:id/dispositions',
      authorize([ tempScope, scopes.campaignsDispositionsWrite, scopes.standard, scopes.legacy.campaignsDispositionsWrite ]),
      wrapAsync(setCampaignDisposition({ logger, targetedCampaignService, campaignCacheService, dispositionsService }, config)));
  }
  // clear account details redis cache
  router.delete(
    '/',
    authorize([ scopes.campaignsCacheDelete, scopes.standard, scopes.legacy.campaignsRead ]),
    wrapAsync(flushCache({ logger, redisClient }, { space: config.space }, config)));
  return router;
};

module.exports = init;
