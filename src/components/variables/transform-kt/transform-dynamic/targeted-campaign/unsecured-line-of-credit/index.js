const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsDate,
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'unsecuredloc';

// is_recommended = true
const validCampaigns = [
  'XCY',
  'XPD',
  'XXN',
  'XXO',
  'YGG',
  'YGH',
  'YGJ',
  'YGK',
];

/**
 * Returns a function that checks if the campaign is a Unsecure LoC campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isUnsecuredLoc = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // TARGET MODULE TYPE: TLP
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Rate (Blank)
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Discount Rate
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // 6
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // PR_Pay1
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // PR_Pay2 (Blank)
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // PR_Pay3
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // PR_Save2 (Blank)
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  // PR_Save3
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // Offer expiry date
  [ R.equals('SOLUI_DATE10_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER10') ],
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER10') ],
  // Rate Date
  [ R.equals('SOLUI_DATE11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  [ R.equals('SOLUI_OTHER11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  // DateDate (Blank except SLV)
  [ R.equals('SOLUI_DATE12_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER12') ],
  [ R.equals('SOLUI_OTHER12_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER12') ],
  // P.PP (Blank except SLV)
  [ R.equals('SOLUI_OTHER13_END'), (x) => processAsIs(deps, campaign, x, 'OTHER13') ],
  // Y.YY (Blank except SLV)
  [ R.equals('SOLUI_OTHER14_END'), (x) => processAsIs(deps, campaign, x, 'OTHER14') ],
  // Z.ZZ (Blank except SLV)
  [ R.equals('SOLUI_OTHER15_END'), (x) => processAsIs(deps, campaign, x, 'OTHER15') ],
  // TDCode (11)
  [ R.equals('SOLUI_OTHER16_END'), (x) => processAsIs(deps, campaign, x, 'OTHER16') ],
  // CampType (TLP)
  [ R.equals('SOLUI_OTHER17_END'), (x) => processAsIs(deps, campaign, x, 'OTHER17') ],
  // MinAmt (Blank)
  [ R.equals('SOLUI_OTHER18_END'), (x) => processAsIs(deps, campaign, x, 'OTHER18') ],
  // BT Fee (Blank)
  [ R.equals('SOLUI_OTHER19_END'), (x) => processAsIs(deps, campaign, x, 'OTHER19') ],
  // Legal Text (Blank)
  [ R.equals('SOLUI_LEGALTEXT_END'), (x) => processAsIs(deps, campaign, x, 'OTHER20') ],
  [ R.equals('SOLUI_OTHER20_END'), (x) => processAsIs(deps, campaign, x, 'OTHER20') ],
  // Card Image (Blank)
  [ R.equals('SOLUI_CARDIMAGE_END'), (x) => processAsIs(deps, campaign, x, 'OTHER21') ],
  [ R.equals('SOLUI_OTHER21_END'), (x) => processAsIs(deps, campaign, x, 'OTHER21') ],
  // BT Fee Text (Blank)
  [ R.equals('SOLUI_BTFEETEXT_END'), (x) => processAsIs(deps, campaign, x, 'OTHER22') ],
  [ R.equals('SOLUI_OTHER22_END'), (x) => processAsIs(deps, campaign, x, 'OTHER22') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],

]);

/**
 * Transforms variables for CEBA 5.0 campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processUnsecuredLoc = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isUnsecuredLoc,
  processUnsecuredLoc,
};
