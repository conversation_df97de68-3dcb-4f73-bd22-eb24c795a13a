const { isUnsecuredLoc, processUnsecuredLoc } = require('./index');

const mockCampaign = {
  message_id: '********',
  campaign_id: 'XCY34',
  additional_data: [
    { name: 'OTHER1', value: '201' },
    { name: 'OTHER2', value: '202' },
    { name: 'OTHER3', value: '203' },
    { name: 'OTHER4', value: '204' },
    { name: 'OTHER5', value: '205' },
    { name: 'OTHER6', value: '206' },
    { name: 'OTHER7', value: '207' },
    { name: 'OTHER8', value: '208' },
    { name: 'OTHER9', value: '209' },
    { name: 'OTHER10', value: '20190101' },
    { name: 'OTHER11', value: '20190101' },
    { name: 'OTHER12', value: '20190101' },
    { name: 'OTHER13', value: '213' },
    { name: '<PERSON>THER14', value: '214' },
    { name: '<PERSON>THER15', value: '215' },
    { name: 'OTHER16', value: '216' },
    { name: '<PERSON>THER17', value: '217' },
    { name: 'OTHER18', value: '218' },
    { name: '<PERSON><PERSON>ER19', value: '219' },
    { name: 'OTHER20', value: '220' },
    { name: 'OTHER21', value: '221' },
    { name: 'OTHER22', value: '222' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_DATE10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_DATE12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_OTHER14_END',
  'SOLUI_OTHER15_END',
  'SOLUI_OTHER16_END',
  'SOLUI_OTHER17_END',
  'SOLUI_OTHER18_END',
  'SOLUI_OTHER19_END',
  'SOLUI_OTHER20_END',
  'SOLUI_LEGALTEXT_END',
  'SOLUI_OTHER21_END',
  'SOLUI_CARDIMAGE_END',
  'SOLUI_OTHER22_END',
  'SOLUI_BTFEETEXT_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > RM7', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isUnsecuredLoc).toBeInstanceOf(Function);
    expect(processUnsecuredLoc).toBeInstanceOf(Function);
  });
  test('should identify XCY campaign as unsecured line of credit', () => {
    expect(isUnsecuredLoc({ campaign_id: 'XCY34' })).toEqual(true);
  });
  test('should NOT identify RM786 campaign as unsecured line of credit', () => {
    expect(isUnsecuredLoc({ campaign_id: 'RM786' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processUnsecuredLoc({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('unsecuredloc');
    const testDate = 'January 1, 2019';
    expect(transformed[0]).toEqual('201');
    expect(transformed[1]).toEqual('202');
    expect(transformed[2]).toEqual('203');
    expect(transformed[3]).toEqual('204');
    expect(transformed[4]).toEqual('205');
    expect(transformed[5]).toEqual('206');
    expect(transformed[6]).toEqual('207');
    expect(transformed[7]).toEqual('208');
    expect(transformed[8]).toEqual('209');
    expect(transformed[9]).toEqual(testDate);
    expect(transformed[10]).toEqual(testDate);
    expect(transformed[11]).toEqual(testDate);
    expect(transformed[12]).toEqual(testDate);
    expect(transformed[13]).toEqual(testDate);
    expect(transformed[14]).toEqual(testDate);
    expect(transformed[15]).toEqual('213');
    expect(transformed[16]).toEqual('214');
    expect(transformed[17]).toEqual('215');
    expect(transformed[18]).toEqual('216');
    expect(transformed[19]).toEqual('217');
    expect(transformed[20]).toEqual('218');
    expect(transformed[21]).toEqual('219');
    expect(transformed[22]).toEqual('220');
    expect(transformed[23]).toEqual('220');
    expect(transformed[24]).toEqual('221');
    expect(transformed[25]).toEqual('221');
    expect(transformed[26]).toEqual('222');
    expect(transformed[27]).toEqual('222');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processUnsecuredLoc({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER0_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
